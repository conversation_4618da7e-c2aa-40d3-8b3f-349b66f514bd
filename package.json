{"name": "pc-building-site", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "db:seed": "tsx prisma/seed.ts"}, "dependencies": {"@hookform/resolvers": "^5.2.1", "@prisma/client": "^6.13.0", "@types/bcryptjs": "^2.4.6", "@types/multer": "^2.0.0", "bcryptjs": "^3.0.2", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.536.0", "multer": "^2.0.2", "next": "15.4.5", "next-auth": "^4.24.11", "prisma": "^6.13.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "tailwind-merge": "^3.3.1", "zod": "^4.0.14"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.5", "jest": "^30.0.5", "node-fetch": "^3.3.2", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}}