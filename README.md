# PC Building Website

A comprehensive PC building website built with Next.js, featuring both customer-facing functionality and admin management capabilities.

## Features

### Customer Features
- **Homepage**: Modern landing page with featured products and navigation
- **Product Browsing**: Browse PC components by category with search functionality
- **PC Builder Tool**: Interactive tool to select components and see total price with compatibility checking
- **Pre-built PCs**: View curated PC builds with FPS estimates for popular games
- **Detailed Product Views**: Comprehensive product information and specifications

### Admin Features
- **Admin Dashboard**: Overview of products, orders, and system statistics
- **Product Management**: Add, edit, and delete products with image upload
- **Pre-built PC Management**: Create and manage pre-configured builds with FPS estimates
- **Order Management**: View and manage customer orders with status tracking
- **Authentication**: Secure admin login system

## Tech Stack

- **Frontend**: Next.js 15.4.5 with App Router, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes
- **Database**: SQLite with Prisma ORM
- **Authentication**: NextAuth.js with credentials provider
- **UI Components**: Lucide React icons, custom Tailwind components
- **Forms**: React Hook Form with Zod validation
- **Testing**: Jest for API endpoint testing

## Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd pc-building-site
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up the database**
   ```bash
   npx prisma generate
   npx prisma db push
   npm run db:seed
   ```

4. **Set up environment variables**
   Create a `.env.local` file in the root directory:
   ```env
   NEXTAUTH_SECRET=your-secret-key-here
   NEXTAUTH_URL=http://localhost:3000
   DATABASE_URL="file:./dev.db"
   ```

5. **Start the development server**
   ```bash
   npm run dev
   ```

6. **Access the application**
   - Customer site: http://localhost:3000
   - Admin login: http://localhost:3000/admin/login
   - Default admin credentials: <EMAIL> / admin123

### Testing

Run the test suite:
```bash
npm test
```

## Project Structure

```
src/
├── app/                    # Next.js App Router pages
│   ├── admin/             # Admin dashboard pages
│   ├── api/               # API routes
│   ├── builder/           # PC builder tool
│   ├── prebuilt/          # Pre-built PCs pages
│   ├── products/          # Product browsing pages
│   └── page.tsx           # Homepage
├── components/            # Reusable UI components
├── lib/                   # Utility functions and configurations
│   ├── auth.ts           # NextAuth configuration
│   ├── prisma.ts         # Prisma client
│   └── utils.ts          # Utility functions
└── middleware.ts          # Route protection middleware

prisma/
├── schema.prisma          # Database schema
└── seed.ts               # Database seeding script
```

## Database Schema

The application uses the following main entities:

- **Admin**: Admin user accounts
- **Category**: Product categories (CPU, GPU, RAM, etc.)
- **Product**: Individual PC components
- **PreBuiltPC**: Pre-configured PC builds
- **Order**: Customer orders
- **OrderItem**: Individual items in orders
- **Build**: Custom PC builds (for future use)

## API Endpoints

### Public Endpoints
- `GET /api/categories` - Get all product categories
- `GET /api/products` - Get all products
- `GET /api/prebuilt` - Get all pre-built PCs
- `POST /api/orders` - Create a new order

### Protected Admin Endpoints
- `POST /api/products` - Create a new product
- `POST /api/prebuilt` - Create a new pre-built PC
- `GET /api/orders` - Get all orders (admin only)

## Deployment

### Vercel (Recommended)

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **Deploy to Vercel**
   - Connect your GitHub repository to Vercel
   - Set environment variables in Vercel dashboard
   - Deploy automatically on push

3. **Database Setup**
   - For production, consider upgrading to PostgreSQL
   - Update DATABASE_URL in environment variables
   - Run migrations: `npx prisma db push`

### Other Platforms

The application can be deployed to any platform that supports Node.js:

- **Netlify**: Use the Next.js build adapter
- **Railway**: Direct deployment with PostgreSQL
- **DigitalOcean App Platform**: Container-based deployment

## Environment Variables

Required environment variables:

```env
NEXTAUTH_SECRET=your-secret-key-here
NEXTAUTH_URL=your-production-url
DATABASE_URL=your-database-connection-string
```

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature-name`
3. Make your changes and test them
4. Commit your changes: `git commit -m 'Add feature'`
5. Push to the branch: `git push origin feature-name`
6. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support or questions, please open an issue in the GitHub repository.
