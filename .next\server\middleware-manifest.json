{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_9f9cf45e._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_88e3134f.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/admin(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\\\.json)?[\\/#\\?]?$", "originalSource": "/admin/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "Z7m/ZmQ6rTuH3ZtbPQXHAUjPJ7izLoSqwRAUJiSzzkw=", "__NEXT_PREVIEW_MODE_ID": "adaaaf6f7ff71f94e812e25d904cc077", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e77a8698661473128b2a2e99c5198860559608a545f31424d037eaccf6a3c5d4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0a363fdd3779776a8c16d6644ead4763560420a73e8ae7275644d70172f7e7cb"}}}, "sortedMiddleware": ["/"], "functions": {}}